page {
  background: rgba(247, 248, 250, 1);
  box-sizing: border-box;
}

.page-container {

  /* 禁用滚动时的样式 */
  &.scroll-disabled {
    position: fixed;
    left: 0;
    right: 0;
    width: 100%;
    /* 保持原有的背景和样式 */
    background: rgba(247, 248, 250, 1);
  }
}

.main-content {
  .header-top {
    padding: 26rpx 32rpx 40rpx 32rpx;
    background: #fff;
    border-bottom: 16rpx solid rgba(247, 248, 250, 1);

    .title {
      font-size: 36rpx;
      color: rgba(34, 36, 46, 1);
      line-height: 54rpx;
      font-weight: bold;
    }

    .label {
      display: flex;
      align-items: center;
      margin-top: 25rpx;

      .adress {
        font-size: 24rpx;
        color: rgba(145, 148, 153, 0.8);
      }

      .time {
        font-size: 24rpx;
        color: rgba(145, 148, 153, 0.8);
        padding-left: 32rpx;
        position: relative;

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 1rpx;
          height: 24rpx;
          left: 16rpx;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(235, 236, 240, 1);
        }
      }
    }
  }

  .content-box-top {
    position: sticky;
    top: var(--header-height, 100px);
    display: flex;
    align-items: center;
    padding: 0 32rpx;
    // padding-top: 40rpx;
    justify-content: space-between;
    background: #fff;
    padding-bottom: 10rpx;
    z-index: 22;
  }

  .content-box {
    background: #fff;
    padding: 0rpx 32rpx 40rpx 32rpx;
    padding-bottom: 48rpx;
    box-sizing: border-box;
  }

  .tab-list {
    display: flex;
    align-items: center;

    &-item {
      font-size: 28rpx;
      color: rgba(102, 102, 102, 1);
      margin-right: 64rpx;
      position: relative;
      padding-top: 40rpx;
      padding-bottom: 34rpx;

      &:last-child {
        margin-right: 0;
      }

      &.active {
        color: rgba(34, 36, 46, 1);
        font-size: 32rpx;
        position: relative;
        font-weight: bold;

        &::after {
          position: absolute;
          display: block;
          content: " ";
          width: 32rpx;
          height: 6rpx;
          background-color: rgba(230, 0, 3, 1);
          bottom: 14rpx;
          left: 50%;
          transform: translateX(-50%);
          border-radius: 5rpx;
        }
      }

      .dian {
        width: 12rpx;
        height: 12rpx;
        position: absolute;
        right: -12rpx;
        top: 40rpx;
      }
    }
  }

  .search-box {
    display: flex;
    align-items: center;
    background: rgba(247, 248, 250, 1);
    width: 170rpx;
    padding: 0 20rpx;
    box-sizing: border-box;
    border-radius: 32rpx;
    height: 64rpx;

    input {
      flex: 1;
      min-width: 0;
      padding-left: 8rpx;
      font-size: 24rpx;
    }

    image {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .content-center {
    box-sizing: border-box;

    .notice-box {
      background: rgba(247, 248, 250, 1);
      padding: 32rpx;
      border-radius: 16rpx;
      margin-bottom: 30rpx;

      // margin-top: 34rpx;
      .notice-box-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 24rpx;
        border-bottom: 1rpx solid rgba(235, 236, 240, 1);
        transform: rotateZ(360deg);
        margin-bottom: 24rpx;
        box-sizing: border-box;

        .left {
          .left-text {
            font-size: 28rpx;
            color: rgba(60, 61, 66, 1);
            margin-right: 16rpx;

            .num {
              font-weight: bold;
            }
          }
        }

        .status {
          font-size: 28rpx;
          color: rgba(19, 191, 128, 1);
          // font-weight: bold;
        }
      }

      .time-line {
        // margin-top: 24rpx;

        &-item {
          display: flex;
          position: relative;
          padding-left: 24rpx;
          padding-bottom: 24rpx;

          &:last-child {
            padding-bottom: 0;
          }

          &.active {
            .dian {
              width: 7px;
              height: 7px;
              background: #fff;
              border: 2rpx solid rgba(230, 0, 3, 1);
              left: -2px;
              top: 10rpx;

              &::after {
                display: block;
                content: " ";
                position: absolute;
                width: 3px;
                height: 3px;
                background-color: rgba(230, 0, 3, 1);
                left: 50%;
                top: 50%;
                border-radius: 50%;
                transform: translate(-50%, -50%);
              }
            }

            .text-word {
              color: rgba(230, 0, 3, 1);
            }

            .time {
              color: rgba(230, 0, 3, 1);
            }
          }

          &::after {
            position: absolute;
            display: block;
            content: " ";
            width: 1px;
            height: 100%;
            border-left: 1px dashed rgba(194, 197, 204, 1);
            left: 2px;
            z-index: 1;
            top: 15rpx;
            box-sizing: border-box;
          }

          &:last-child {
            &::after {
              display: none;
            }
          }

          .text-word {
            font-size: 24rpx;
            color: rgba(60, 61, 66, 1);
            min-width: 120rpx;
          }

          .time {
            font-size: 24rpx;
            color: rgba(145, 148, 153, 1);
            padding-left: 16rpx;
            flex: 1;
            min-width: 0;
          }

          .dian {
            width: 5px;
            height: 5px;
            background-color: rgba(194, 197, 204, 1);
            border-radius: 50%;
            position: absolute;
            z-index: 2;
            left: 0;
            top: 15rpx;
          }
        }
      }

      .source {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24rpx;
        color: rgba(60, 61, 66, 1);
        margin-top: 8rpx;

        .time {
          color: rgba(68, 138, 255, 1);
        }

        image {
          width: 32rpx;
          height: 32rpx;
          display: block;
        }
      }
    }

    .text-box {
      margin-top: 30rpx;
      font-size: 32rpx;
      line-height: 64rpx;
      color: rgba(60, 61, 66, 1);

      &.collapsed {
        height: 200rpx;
        overflow: hidden;
      }
    }

    // 新的rich-text容器样式
    .rich-text-container {
      // margin-top: 30rpx;
      font-size: 32rpx;
      line-height: 64rpx;
      color: rgba(60, 61, 66, 1);
      position: relative;
      transition: max-height 0.3s ease;

      &.collapsed {
        position: relative;
      }

      &.expanded {
        max-height: none !important;
      }

      rich-text {
        display: block;
        word-wrap: break-word;
        word-break: break-all;
        font-size: 32rpx !important;

        // 富文本图片统一样式
        .rich-text-img {
          max-width: 100% !important;
          height: auto;
          display: block;
          margin: 10rpx 0;
        }

        // 富文本表格包装器样式
        .rich-text-table-wrapper {
          width: 100%;
          overflow-x: auto;
          margin: 20rpx 0;
          border-radius: 8rpx;
          padding: 1px; // 添加内边距防止边框被裁剪
          box-sizing: border-box;

          table {
            width: 100% !important;
            min-width: 100% !important;
            border-collapse: collapse !important;
            font-size: 28rpx !important;
            border: none !important;
            table-layout: auto !important;
            margin: -1px; // 负边距抵消容器的padding
          }
        }
      }
    }
  }

  .see-all {
    font-size: 32rpx;
    color: rgba(60, 61, 66, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    margin-top: 0rpx;
    padding-top: 0rpx;
    box-sizing: border-box;
    // padding-bottom: 60rpx;

    &.collapsed {
      image {
        transform: rotate(0deg);
      }
    }

    image {
      width: 32rpx;
      height: 32rpx;
      transform: rotate(180deg);
    }
  }

  .see-address {
    background: linear-gradient(138deg, #fff3f0 0%, #ffeeeb 74%, #ffe5e0 100%);
    padding: 30rpx 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
    color: rgba(230, 0, 3, 1);
    border-radius: 16rpx;
    margin-top: 64rpx;

    image {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .originally {
    // display: flex;
    // flex-wrap: wrap;
    font-size: 26rpx;
    margin-top: 40rpx;
    line-height: 40rpx;

    .title {
      color: rgba(102, 102, 102, 1);
      // text-decoration: underline;
    }

    image {
      width: 32rpx;
      height: 32rpx;
      margin-left: 10rpx;
      transform: translateY(6rpx);
      margin-right: 4rpx;
    }

    .copy-c {
      font-size: 24rpx;
      color: rgba(95, 126, 149, 1);
    }
  }

  .course-content {
    padding: 40rpx 32rpx;

    .title {
      font-size: 32rpx;
      color: rgba(34, 36, 46, 1);
    }
  }

  .attachment-box {
    background: rgba(247, 248, 250, 1);
    padding: 32rpx;
    border-radius: 16rpx;
    margin-top: 60rpx;

    .title {
      font-size: 32rpx;
      color: rgba(34, 36, 46, 1);
      font-weight: bold;
    }

    .informaiton-list {
      margin-top: 24rpx;

      &-item {
        display: flex;
        align-items: center;
        background: #fff;
        padding: 24rpx 32rpx;
        border-radius: 12rpx;
        box-sizing: border-box;
        margin-bottom: 16rpx;

        &:last-child {
          margin-bottom: 0;
        }

        .type-img {
          width: 64rpx;
          height: 64rpx;
        }

        .text {
          padding: 0 32rpx;
          font-size: 24rpx;
          color: rgba(60, 61, 66, 1);
          flex: 1;
          line-height: 36rpx;
        }

        .right-arrow {
          width: 32rpx;
          height: 32rpx;
        }
      }

      .informaiton-item {
        .fu-j {
          font-size: 26rpx;
          color: rgba(60, 61, 66, 1);
        }

        .content {
          font-size: 24rpx;
          color: rgba(102, 102, 102, 1);
        }

        .copy-img {
          width: 32rpx;
          height: 32rpx;
          transform: translateY(6rpx);
          margin-right: 4rpx;
          margin-left: 10rpx;
        }

        .copy-c {
          font-size: 22rpx;
          color: rgba(95, 126, 149, 1);
        }
      }
    }
  }
}

.bor_bb {
  border-bottom: 16rpx solid rgba(247, 248, 250, 1);
}

.popular-answer {
  padding: 48rpx 32rpx 40rpx 32rpx;
  background: #fff;

  .item-title {
    font-size: 32rpx;
    color: rgba(34, 36, 46, 1);
    font-weight: bold;
  }

  .answer-list {
    margin-top: 30rpx;
  }

  .answer-list-item {
    margin-bottom: 30rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .ask-box {
    display: flex;

    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
      margin-top: 15rpx;
    }

    .text-t {
      font-size: 30rpx;
      line-height: 60rpx;
      color: rgba(60, 61, 66, 1);
      flex: 1;
    }
  }

  .answer-box {
    display: flex;
    margin-top: 10rpx;

    image {
      width: 32rpx;
      height: 32rpx;
      margin-right: 16rpx;
      margin-top: 6rpx;
    }

    .text-t {
      font-size: 26rpx;
      line-height: 40rpx;
      color: rgba(102, 102, 102, 1);
      flex: 1;
    }
  }

  .more-button {
    font-size: 24rpx;
    color: rgba(60, 61, 66, 1);
    background: rgba(247, 248, 250, 1);
    border: 1rpx solid rgba(194, 197, 204, 0.5);
    transform: rotateZ(360deg);
    height: 80rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 590rpx;
    margin: 0 auto;
    margin-top: 40rpx;

    image {
      width: 32rpx;
      height: 32rpx;
      margin-top: 4rpx;
    }
  }
}

.tool-box {
  padding: 48rpx 32rpx;
  background: #fff;

  .item-title {
    font-size: 32rpx;
    color: rgba(34, 36, 46, 1);
    font-weight: bold;
  }

  .tool-list {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-top: 32rpx;

    &-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 25%;
      font-size: 24rpx;
      color: rgba(60, 61, 66, 1);
      margin-bottom: 48rpx;

      image {
        width: 64rpx;
        height: 64rpx;
        margin-bottom: 10rpx;
      }
    }
  }
}

.statement-box {
  padding: 40rpx 30rpx;
  background: rgba(247, 248, 250, 1);
  font-size: 22rpx;
  color: rgba(194, 197, 204, 1);
  line-height: 32rpx;
  // background: linear-gradient(
  //   89deg,
  //   rgba(255, 255, 255, 0) 0%,
  //   rgba(255, 243, 211, 0.2) 50%,
  //   rgba(255, 255, 255, 0) 100%
  // );
}

.action-bar-box {
  display: flex;
  height: 142rpx;
  z-index: 998;
  position: relative;

  box-sizing: border-box;
}

.action-bar .button-area {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .button {
    width: calc(50% - 7rpx);
    background-color: var(--main-color);
    font-size: 30rpx;
    color: #fff;
    height: 84rpx;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .w100 {
    width: 100% !important;
  }

  .next-btn {
    background: #ffffff;
    border: 1rpx solid rgba(230, 0, 0, 0.6);
    transform: rotateZ(360deg);
    color: #e60000;
  }
}

.action-bar {
  z-index: 1;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 142rpx;
  background-color: #fff;
  box-sizing: border-box;
  border-top: 1rpx solid #ebecf0;
  transform: rotateZ(360deg);
  padding: 24rpx 40rpx 34rpx 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bottom-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: 40rpx;
  position: relative;

  .new-box {
    position: absolute;
    font-size: 18rpx;
    color: rgba(230, 0, 3, 1);
    background: rgba(230, 0, 3, 0.1);
    padding: 2rpx 10rpx;
    right: -14rpx;
    border-radius: 20rpx 20rpx 20rpx 4rpx;
    top: -5px;
  }

  image {
    width: 40rpx;
    height: 40rpx;
    margin-bottom: 4rpx;
  }

  font-size: 20rpx;
  color: rgba(145, 148, 153, 1);
}

.filter-button {
  background: rgba(236, 62, 51, 1);
  font-size: 28rpx;
  color: #fff;
  // flex: 1;
  // min-width: 0;
  width: 400rpx;
  height: 84rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
}

.flex-c {
  display: flex;
  align-items: center;
}

.select-box-content {
  padding: 0rpx 32rpx 20rpx 32rpx;

  // 吸顶时的边框样式
  &.sticky-border {
    border-bottom: 1px solid rgba(235, 236, 240, 1);
  }

  .select-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(247, 248, 250, 1);
    padding: 16rpx;
    border-radius: 12rpx;
    border: 1rpx solid rgba(235, 236, 240, 1);
    transform: rotateZ(360deg);

    &.active {
      .img {
        transform: rotate(180deg);
      }
    }

    .title {
      font-size: 26rpx;
      color: rgba(102, 102, 102, 1);
      flex: 1;
      min-width: 0;
    }

    .img {
      width: 32rpx;
      height: 32rpx;
    }
  }

  .menu-box {
    padding: 32rpx 0rpx 0rpx 0;
    position: relative;

    .right-btn {
      position: absolute;
      right: 0;
      top: 32rpx;
      padding: 0 45rpx;
      padding-right: 0;
      height: 58rpx;
      display: flex;
      align-items: center;
      background: #fff;

      &::after {
        position: absolute;
        display: block;
        height: 54rpx;
        content: " ";
        width: 1rpx;
        left: 16rpx;
        background-color: rgba(235, 236, 240, 1);
      }

      .img {
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
}

.notes-box {
  height: 140rpx;
  width: 100%;
  box-sizing: border-box;
  // background: rgba(255, 247, 247, 1);
  display: flex;
  align-items: center;
  padding: 0 32rpx;
  position: relative;
  margin-bottom: 24rpx;
  border-radius: 16rpx;

  .notes-bg {
    width: 100%;
    position: absolute;
    height: 100%;
    left: 0;
    border-radius: 16rpx;
  }

  .circle-text {
    font-size: 20rpx;
    color: rgba(60, 61, 66, 1);

    .num {
      font-size: 28rpx;
      font-weight: bold;
      font-family: "DINBold";
    }
  }

  .content-text {
    padding-left: 24rpx;
    flex: 1;
    position: relative;

    .title {
      font-size: 26rpx;
      color: rgba(60, 61, 66, 1);
    }

    .label {
      font-size: 22rpx;
      color: rgba(145, 148, 153, 1);
      margin-top: 4rpx;
    }
  }

  .improve-btn {
    position: relative;
    font-size: 24rpx;
    color: rgba(236, 62, 51, 1);
    width: 136rpx;
    height: 50rpx;
    border: 1px solid rgba(236, 62, 51, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12rpx;
    margin-right: 32rpx;
    box-sizing: border-box;
  }

  .close-img-box {
    position: absolute;
    top: 0;
    right: 0;
    padding-top: 8rpx;
    padding-right: 18rpx;
  }

  .close-img {
    width: 24rpx;
    height: 24rpx;
  }
}

.pt120 {
  padding-bottom: 120rpx;
}

.p32 {
  padding: 32rpx;
}

.official-list {
  padding: 32rpx;

  &-item {
    background: rgba(255, 255, 255, 1);
    padding: 32rpx;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .new-label {
      position: absolute;
      right: 0;
      top: 0;
      font-size: 16rpx;
      color: #fff;
      background: rgba(230, 0, 3, 0.6);
      border-radius: 0rpx 8rpx 0rpx 8rpx;
      padding: 0rpx 8rpx;
      height: 26rpx;
      display: flex;
      align-items: center;
      box-sizing: border-box;
    }

    .time {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      &::after {
        position: absolute;
        display: block;
        content: " ";
        width: 1rpx;
        height: 92rpx;
        background-color: rgba(235, 236, 240, 1);
        right: -38rpx;
      }

      .day {
        font-size: 20rpx;
        color: rgba(102, 102, 102, 1);

        .num {
          font-size: 32rpx;
          color: rgba(102, 102, 102, 1);
          font-family: "DINBold";
        }
      }

      .month {
        font-size: 20rpx;
        color: rgba(194, 197, 204, 1);

        .num {
          font-size: 24rpx;
          color: rgba(194, 197, 204, 1);
          font-family: "DINBold";
        }
      }

      .line {
        width: 2rpx;
        height: 12rpx;
        background: rgba(235, 236, 240, 1);
        transform: rotate(45deg);
      }
    }

    .title {
      font-size: 26rpx;
      flex: 1;
      min-width: 0;
      padding-left: 70rpx;
      color: rgba(60, 61, 66, 1);
      line-height: 40rpx;
      padding-right: 16rpx;
    }

    .arrow {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.popu-box {
  // padding: 0 32rpx;
  box-sizing: border-box;
  background: rgba(247, 248, 250, 1);
  height: 100%;
  display: flex;
  flex-direction: column;

  .popu-box-top {
    background: rgba(255, 255, 255, 1);
    padding: 32rpx 32rpx 24rpx 32rpx;
    position: relative;

    .title {
      font-size: 28rpx;
      text-align: center;

      .num {
        font-size: 28rpx;
        color: rgba(230, 0, 3, 1);
      }
    }

    .close {
      position: absolute;
      top: 36rpx;
      right: 32rpx;
      width: 32rpx;
      height: 32rpx;
    }

    .popu-menu {
      padding-top: 40rpx;

      .top {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .menu-item {
        padding: 10rpx 14rpx 10rpx 20rpx;
        display: flex;
        align-items: center;
        font-size: 24rpx;
        color: rgba(60, 61, 66, 1);
        border: 1rpx solid rgba(225, 226, 230, 1);
        transform: rotateZ(360deg);
        border-radius: 8rpx;

        &.active {
          border-color: rgba(230, 0, 3, 0.5);
          color: rgba(230, 0, 3, 1);
        }

        &.zhuan {
          .img {
            transform: rotate(-180deg);
          }
        }

        .img {
          width: 32rpx;
          height: 32rpx;
        }
      }

      .right-menu {
        display: flex;

        .menu-item {
          margin-right: 16rpx;

          &:last-child {
            margin-right: 0;
          }
        }
      }

      .bottom-menu {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 32rpx;

        .bottom-menu-item {
          width: 218rpx;
          background: rgba(247, 248, 250, 1);
          border-radius: 8rpx;
          font-size: 22rpx;
          color: rgba(102, 102, 102, 1);
          height: 54rpx;
          display: flex;
          align-items: center;
          justify-content: center;

          &.active {
            background: rgba(230, 0, 3, 0.05);
            color: rgba(230, 0, 3, 1);
          }
        }
      }
    }
  }

  .popu-box-content {
    background: rgba(247, 248, 250, 1);
    padding: 32rpx 0;
    flex: 1;
    min-height: 0;
  }
}

.van-popup {
  background: rgba(247, 248, 250, 1) !important;
}

.position-card-list {
  box-sizing: border-box;
  height: 100%;

  .p-32 {
    padding: 0 32rpx;
  }

  // max-height: 650rpx;

  .position-card {
    background: rgba(255, 255, 255, 1);
    border-radius: 16rpx;
    padding: 32rpx;
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .title {
      font-size: 32rpx;
      color: rgba(34, 36, 46, 1);
      font-weight: bold;
    }

    .time {
      font-size: 24rpx;
      color: rgba(145, 148, 153, 1);
      margin-top: 14rpx;
    }

    .bottom {
      display: flex;
      align-items: center;
      padding-top: 34rpx;
      border-top: 1rpx solid rgba(235, 236, 240, 1);
      transform: rotateZ(360deg);
      margin-top: 32rpx;

      .text-word {
        flex: 1;
        min-width: 0;
        font-size: 24rpx;
        color: rgba(145, 148, 153, 1);

        .num {
          color: rgba(60, 61, 66, 1);
          margin: 0 4rpx;

          &.ml16 {
            margin-left: 16rpx;
          }
        }
      }

      .all-btn {
        width: 152rpx;
        height: 54rpx;
        background-color: rgba(230, 0, 3, 0.8);
        font-size: 22rpx;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8rpx;
      }

      .status {
        font-size: 24rpx;
        color: rgba(19, 191, 128, 0.8);

        &.blue {
          color: rgba(68, 138, 255, 0.8);
        }

        &.over {
          color: rgba(255, 106, 77, 0.8);
        }

        &.end {
          color: rgba(145, 148, 153, 1);
        }
      }

      .line {
        font-size: 20rpx;
        color: rgba(194, 197, 204, 1);
        margin: 0 8rpx;
      }

      .item-text {
        font-size: 24rpx;
        color: rgba(145, 148, 153, 1);

        .num {
          color: rgba(60, 61, 66, 1);
        }
      }

      .mr16 {
        margin-right: 16rpx;
      }

      .flex-v {
        display: flex;
        align-items: center;
        flex: 1;
        min-width: 0;
      }
    }
  }

  .title-line {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
    color: rgba(145, 148, 153, 1);
    padding-top: 40rpx;

    .text {
      margin: 0 16rpx;
    }

    .img {
      width: 106rpx;
      height: 6rpx;
    }
  }

  .card-list {
    padding-top: 24rpx;
  }
}

.lefts {
  padding-left: 32rpx;
  display: flex;
  align-items: center;

  .left-arrow {
    width: 40rpx;
    height: 40rpx;
  }
}

.rights {
  display: flex;

  .collection-img {
    width: 40rpx;
    height: 40rpx;
    margin-left: 34rpx;
  }
}

.major-box {
  box-sizing: border-box;
  height: 100%;
  display: flex;
  flex-direction: column;

  .top {
    padding: 32rpx 40rpx;
    border-bottom: 1rpx solid rgba(235, 236, 240, 1);
    transform: rotateZ(360deg);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .close {
      font-size: 28rpx;
      color: rgba(194, 197, 204, 1);
    }

    .title {
      font-size: 32rpx;
      color: rgba(49, 52, 54, 1);
      font-weight: bold;
    }

    .confirm {
      font-size: 28rpx;
      color: rgba(230, 0, 3, 1);
    }
  }

  .major-content {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .major-content-top {
      padding: 40rpx;
      border-bottom: 1rpx solid rgba(235, 236, 240, 1);
      transform: rotateZ(360deg);

      .title {
        font-size: 30rpx;
        color: rgba(49, 52, 54, 1);
        font-weight: bold;
      }

      .label-list {
        display: flex;
        margin-top: 16rpx;
      }

      .label {
        font-size: 22rpx;
        height: 40rpx;
        display: flex;
        align-items: center;
        padding: 0 8rpx;
        border-radius: 8rpx;
        color: rgba(230, 0, 3, 1);
        border: 1rpx solid rgba(230, 0, 3, 0.5);
        transform: rotateZ(360deg);
        box-sizing: border-box;
      }

      .select-is {
        background: rgba(247, 248, 250, 1);
        color: rgba(49, 52, 54, 1);
        font-size: 26rpx;
        border-radius: 16rpx;
        padding: 24rpx;
        margin-top: 32rpx;

        .left {
          color: rgba(49, 52, 54, 1);
          font-weight: bold;
        }
      }

      .search-box {
        border: 2rpx solid rgba(235, 236, 240, 1);
        padding: 22rpx 24rpx;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        margin-top: 24rpx;

        input {
          font-size: 28rpx;
          flex: 1;
          min-width: 0;
        }

        .img {
          width: 32rpx;
          height: 32rpx;
        }
      }
    }

    .major-contetnt-center {
      flex: 1;
      min-height: 0;
      display: flex;

      .select-center-item {
        width: 33.33333%;
        // border-right: 1rpx solid rgba(235, 236, 240, 1);
        position: relative;

        &:last-child {
          &::after {
            display: none;
          }
        }

        &::after {
          position: absolute;
          content: "";
          display: block;
          width: 1rpx;
          height: 100%;
          background: rgba(235, 236, 240, 1);
          right: 1rpx;
          top: 0;
        }

        .one-text {
          font-size: 26rpx;
          padding: 24rpx 32rpx 24rpx 32rpx;
          background: rgba(247, 248, 250, 1);
          display: flex;
          align-items: center;
          justify-content: space-between;
          line-height: 40rpx;

          .num {
            min-width: 32rpx;
            min-height: 32rpx;
            background: rgba(230, 0, 3, 0.1);
            font-size: 20rpx;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(230, 0, 3, 1);
            font-family: "DINGBold";

            &.active {
              background: rgba(230, 0, 3, 1);
              color: #fff;
            }
          }

          &.active {
            background: #fff;
            position: relative;
            color: rgba(230, 0, 3, 1);
            font-weight: bold;

            &::after {
              display: block;
              position: absolute;
              content: " ";
              width: 1rpx;
              height: 100%;
              background-color: #fff;
              top: 0;
              right: 1rpx;
              z-index: 2;
            }
          }
        }

        .two-text {
          font-size: 26rpx;
          color: rgba(60, 61, 66, 1);
          padding: 24rpx 32rpx;

          &.active {
            font-weight: bold;
            color: rgba(230, 0, 3, 1);
          }
        }
      }
    }
  }
}

.entrance-box {
  position: fixed;
  width: 140rpx;
  right: 24rpx;
  bottom: 280rpx;
  font-size: 24rpx;
  color: rgba(230, 0, 3, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  image {
    width: 140rpx;
  }

  text {
    font-size: 26rpx;
    display: block;
    font-weight: bold;
  }
}

.gradient-height {
  height: 60rpx;
  width: 100%;
  box-sizing: border-box;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 100%);
  position: absolute;
  bottom: 100rpx; // 调整位置以适配新的展开按钮位置
  left: 0;
  z-index: 1;
}

.pr-box {
  position: relative;
}

.bottom-pos {
  // position: absolute;
  // bottom: 0rpx;
  // left: 0;
  // right: 0;
  box-sizing: border-box;
}

.pos-box {
  padding-bottom: 64rpx;
  border-bottom: 1rpx solid #ebecf0;
  transform: rotateZ(360deg);
}

// 弹窗内容样式
.popu-content {
  background: #fff;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .popu-content-c {
    padding: 24rpx 32rpx;
    // margin-bottom: 40rpx;
    background: #fff;
    box-sizing: border-box;
    max-height: 450rpx;
  }
}

// 考试列表样式
.exam-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;

  &-item {
    padding: 16rpx 24rpx;
    background: #f8f9fa;
    border-radius: 8rpx;
    font-size: 26rpx;
    color: #333;
    border: 1rpx solid transparent;

    &.active {
      background: #fff2f2;
      color: #e60003;
      border-color: #e60003;
    }
  }
}

.exam-category-list {
  padding-bottom: 24rpx;

  .exam-category-item {
    font-size: 26rpx;
    background: rgba(247, 248, 250, 1);
    padding: 26rpx 0;
    border-radius: 12rpx;
    margin-bottom: 16rpx;
    text-align: center;

    &.active {
      background: rgba(235, 236, 240, 1);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// 排序列表样式
.sort-list {
  display: flex;
  flex-direction: column;
  padding-bottom: 24rpx;

  &-item {
    padding: 24rpx 0;
    font-size: 26rpx;
    color: rgba(102, 102, 102, 1);
    position: relative;
    text-align: center;

    &:last-child {
      border-bottom: none;
    }

    &.active {
      color: #e60003;

      &::after {
        content: "";
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 32rpx;
        height: 32rpx;
        background: url("https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_integrate/images/correct/check_red.png") no-repeat center;
        background-size: contain;
      }
    }
  }
}

// 筛选组样式
.group-list {
  margin-bottom: 40rpx;

  &:last-child {
    margin-bottom: 24rpx;
  }

  .title {
    font-size: 28rpx;
    color: #333;
    font-weight: bold;
    margin-bottom: 24rpx;

    .label-text {
      font-size: 24rpx;
      color: #999;
      font-weight: normal;
      margin-left: 8rpx;
    }
  }
}

.group-list-item {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  /* 3列 */
  gap: 24rpx;
  /* 列之间的间距 */
}

.group-item {
  padding: 16rpx 0;
  text-align: center;
  background: rgba(235, 236, 240, 0.6);
  border-radius: 12rpx;
  font-size: 26rpx;
  color: rgba(60, 61, 66, 1);

  &.selected {
    background: #fff2f2;
    color: #e60003;
    border-color: #e60003;
  }
}

// 内容区域样式
.popup-body {
  background: #fff;
  max-height: 60vh;
  overflow-y: auto;

  .select-list {
    padding: 32rpx;
    max-height: 600rpx;
    box-sizing: border-box;

    .select-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 26rpx 24rpx;
      background: rgba(247, 248, 250, 1);
      border-radius: 12rpx;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .item-title {
        font-size: 28rpx;
        color: rgba(102, 102, 102, 1);
        flex: 1;
        min-width: 0;
        padding-right: 30rpx;
      }

      .check-icon {
        width: 32rpx;
        height: 32rpx;
        margin-left: 16rpx;
      }

      &.selected {
        .item-title {
          color: rgba(60, 61, 66, 1);
          font-weight: 500;
        }
      }

      &:active {
        background-color: #f5f5f5;
      }
    }
  }
}

/* 考试地区选择弹窗样式 */
.exam-region-popup-filter {
  z-index: 1000;
}

.exam-region-popup-filter .popup-header {
  padding: 32rpx;
  border-bottom: 1px solid #f5f5f5;
  background: #fff;
}

.exam-region-popup-filter .popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.exam-region-popup-filter .popup-body {
  background: #fff;
  min-height: 400rpx;
}

.written-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
  box-sizing: border-box;
  margin-top: 32rpx;

  &-item {
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    height: 136rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding-right: 24rpx;
    background: linear-gradient(270deg,
        rgba(247, 248, 250, 0.5) 0%,
        #f7f8fa 100%);

    .img {
      width: 80rpx;
      height: 80rpx;
    }

    .title {
      font-size: 28rpx;
      padding-left: 40rpx;
      color: rgba(60, 61, 66, 1);
    }
  }
}

.bd-box {
  .bd-img {
    width: 100%;
    border-radius: 16rpx;
    object-fit: cover;
    max-height: 220rpx;
  }
}

.wp100 {
  width: 100%;
}

.bt_0 {
  border-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* 选择器样式 */
.top-area {
  .van-picker__confirm {
    color: #e60003 !important;
  }
}

.column-area {
  .van-picker-column__item {
    &.van-picker-column__item--selected {
      color: #3c3d42;
    }
  }
}

.active-item {
  color: #3c3d42;
}

.pt0 {
  padding-top: 0 !important;

  .right-btn {
    top: 0 !important;
  }
}

.pt32 {
  padding-top: 32rpx !important;
}

.dians {
  margin: 0 5rpx;
}